# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Intentional** is an AI-powered portfolio builder platform that transforms complex portfolio creation into a guided, personalized experience using intelligent AI agents. The platform eliminates decision paralysis and helps users create professional portfolios through step-by-step AI assistance.

## Project Status

This project is currently in the **planning and specification phase**. The actual codebase implementation has not yet begun. All development should follow the specifications outlined in the documentation and Cursor rules.

## Technology Stack

### Frontend
- **React.js** with TypeScript (functional components with hooks exclusively)
- **Vite** for build tooling and fast development
- **React Query** for server state management and caching
- **React Hook Form** for all form state and validation
- **TailwindCSS** for utility-first styling
- **Shadcn UI** for consistent component library
- **Framer Motion** for animations and transitions
- **Lucide Icons** for consistent iconography
- **Zod** for TypeScript schema validation

### Backend
- **AWS Serverless Architecture** (Lambda, API Gateway, CloudFormation/CDK)
- **Node.js** with **Express.js** and **TypeScript**
- **DynamoDB** for NoSQL data storage
- **OpenAI GPT-4** integration for AI agent capabilities
- **AWS S3** for static assets, **CloudFront** for CDN

### Payment & Integrations
- **Stripe/PayPal** for payment processing
- **OpenAI API** for AI-powered content generation and guidance

## Package Management

**IMPORTANT**: Always use `pnpm` instead of npm or yarn:
```bash
# Install dependencies
pnpm install

# Add new packages
pnpm add <package-name>

# Run scripts
pnpm run <script>

# Use pnpx instead of npx
pnpx <command>
```

## Development Commands

Since the codebase is not yet implemented, standard commands will be:

```bash
# Development server
pnpm dev

# Build for production
pnpm build

# Run tests
pnpm test

# Lint code
pnpm lint

# Type checking
pnpm type-check
```

## Code Standards & Architecture

### TypeScript Guidelines
- Always use `type` instead of `interface` for type definitions
- Use strict TypeScript configuration with explicit return types
- Leverage Zod for runtime schema validation and type inference
- Prefer TypeScript over JavaScript throughout the project

### Component Architecture
- Use functional components with hooks exclusively
- Keep all files under 250 lines of code
- Implement proper component composition patterns
- Use React.memo() for performance optimization when needed
- Create custom hooks to encapsulate complex logic

### State Management Strategy
- **React Query** for server state management and caching
- **React Hook Form** for all form state and validation  
- React's built-in state (useState, useReducer) for local state
- React Context for deeply nested state (avoid prop drilling)

### Styling Approach
- Use **TailwindCSS** utility-first approach exclusively
- **Shadcn UI** components as primary building blocks
- **Framer Motion** for complex animations and page transitions
- **Lucide Icons** for consistent iconography
- Implement responsive design with mobile-first approach

### Backend Patterns
- Keep Lambda functions focused and under 250 lines
- Use Express.js with TypeScript for web application framework
- Implement proper middleware for authentication, logging, error handling
- Follow RESTful API design principles
- Use Zod schemas for request validation

## AI Integration Architecture

### Core AI Agent Capabilities
- **Intelligent Onboarding**: Dynamic questionnaires adapting to user responses
- **Content Strategy AI**: Project selection guidance, content optimization, skill highlighting
- **Personalized Recommendations**: Template suggestions, color schemes, content structure
- **AI-Powered Review**: Content analysis and improvement suggestions

### OpenAI Integration Patterns
- Use structured prompts for consistent AI responses
- Implement proper API key management using AWS Secrets Manager
- Handle OpenAI API errors gracefully with fallback responses
- Implement proper rate limiting and quota management
- Use function calling capabilities for structured interactions

### AI Response Processing
- Validate all AI-generated content before presenting to users
- Use structured response formats (JSON) for programmatic handling
- Implement content filtering for inappropriate responses
- Cache frequently requested AI responses for performance

## Project Structure Guidelines

- Separate mock data from source code
- Keep code clean and eliminate redundant code
- Use clear, descriptive file and directory naming
- Implement proper barrel exports (index.ts) for clean imports
- Separate concerns: components, hooks, utils, types, constants
- No comments in source code by default unless absolutely necessary

## Security & Performance Requirements

### Security Standards
- Implement JWT-based authentication
- Validate all inputs using Zod schemas
- Use HTTPS everywhere and proper encryption
- Implement proper CORS configuration
- Store sensitive data in AWS Secrets Manager

### Performance Targets
- Page load time < 2 seconds for 95% of requests
- AI response time < 5 seconds for content suggestions
- 99.5% uptime SLA for production environment
- Mobile-first responsive design across all devices

## Core Features Implementation

### 1. AI Agent Core
- Dynamic questionnaire framework
- Career path detection algorithms
- Skills assessment and gap identification
- Goal setting with timeline establishment

### 2. Portfolio Builder
- Guided workflow system with step-by-step progression
- Progress tracking and completion indicators
- Smart validation preventing incomplete sections
- Content management (projects, skills, experience, achievements)

### 3. Monetization System
- Freemium model with tiered pricing
- Stripe/PayPal integration for payments
- Feature gating based on subscription levels
- Trial functionality for premium features

## Development Phases

The project follows a 3-phase development approach:

1. **Phase 1 (MVP)**: Core AI agent, basic portfolio builder, user auth, payment integration
2. **Phase 2**: Enhanced AI capabilities, expanded templates, analytics dashboard
3. **Phase 3**: Enterprise features, performance optimization, public launch

## Quality Standards

- Write unit tests for critical business logic
- Implement integration tests for user workflows
- Maintain high test coverage for critical paths
- Use React Testing Library for component testing
- Mock external dependencies appropriately

## Environment Management

- Use separate environments (dev, staging, production)
- Implement proper configuration management
- Use AWS Parameter Store or Secrets Manager for sensitive data
- Implement blue-green or canary deployment strategies

## Key Architectural Decisions

- **Serverless-first**: AWS Lambda for scalability and cost efficiency
- **AI-driven UX**: OpenAI integration for intelligent guidance throughout user journey
- **Component composition**: Shadcn UI + TailwindCSS for consistent, maintainable UI
- **Type safety**: TypeScript + Zod for runtime validation and compile-time safety
- **Performance-focused**: React Query for efficient data fetching and caching