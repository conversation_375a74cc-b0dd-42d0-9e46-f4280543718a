# Intentional

AI-Powered Portfolio Builder Platform

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Node.js](https://img.shields.io/badge/node-%3E%3D16.x-brightgreen.svg)
![React](https://img.shields.io/badge/react-%5E18.x-blue.svg)
![TypeScript](https://img.shields.io/badge/typescript-%5E4.x-blue.svg)

## Project Overview

**Intentional** is an AI-powered portfolio builder platform that transforms the complex process of portfolio creation into a guided, personalized experience using intelligent AI agents. By leveraging an intelligent AI Agent, the platform eliminates decision paralysis and helps users create professional portfolios that effectively showcase their skills, experiences, and projects.

### Key Value Proposition

- **Guided workflow** instead of overwhelming blank canvas
- **AI-driven personalization** based on user's background and goals
- **Step-by-step assistance** with intelligent questioning and suggestions
- **Professional results** without design or technical expertise

## Features

### AI Agent Core Capabilities

- **Intelligent Onboarding**: Dynamic questionnaire adapting based on user responses
- **Career Path Detection**: Industry-specific guidance and recommendations
- **Skills Assessment**: Gap identification and proficiency evaluation
- **Content Strategy AI**: Project selection guidance and content optimization
- **Personalized Recommendations**: Template suggestions and design recommendations

### Portfolio Builder Features

- **Guided Workflow System**: Step-by-step progression with clear milestones
- **Content Management**: Project showcase, skills matrix, experience timeline
- **Design & Customization**: AI-recommended templates and responsive design
- **Collaboration & Feedback**: AI-powered content review and improvement suggestions

### Monetization System

- **Freemium Model**: Free tier with basic features, premium tiers with advanced capabilities
- **Subscription Tiers**: 
  - Free: $0/month - Basic portfolio creation
  - Premium: $19/month - Unlimited AI assistance and premium templates
  - Professional: $49/month - Advanced analytics and custom branding

## Technology Stack

### Frontend

- **React.js** with TypeScript (functional components with hooks)
- **Vite** for build tooling and fast development
- **React Query** for server state management and caching
- **React Hook Form** for form state and validation
- **TailwindCSS** for utility-first styling
- **Shadcn UI** for consistent component library
- **Framer Motion** for animations and transitions
- **Lucide Icons** for consistent iconography
- **Zod** for TypeScript schema validation

### Backend

- **AWS Serverless Architecture** (Lambda, API Gateway, CloudFormation/CDK)
- **Node.js** with **Express.js** and **TypeScript**
- **DynamoDB** for NoSQL data storage
- **OpenAI GPT-4** integration for AI agent capabilities
- **AWS S3** for static assets, **CloudFront** for CDN

### Payment & Integrations

- **Stripe/PayPal** for payment processing
- **OpenAI API** for AI-powered content generation and guidance

## Installation

This project uses **pnpm** as the package manager. Make sure you have pnpm installed:

```bash
npm install -g pnpm
```

### Prerequisites

- Node.js >= 16.x
- pnpm >= 7.x
- AWS CLI configured (for backend deployment)
- OpenAI API key (for AI features)

### Setup

1. Clone the repository:

```bash
git clone https://github.com/your-username/intentional.git
cd intentional
```

2. Install dependencies:

```bash
pnpm install
```

3. Create a `.env` file in the root directory with the following variables:

```env
# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region

# Database
DATABASE_URL=your_database_url

# Authentication
JWT_SECRET=your_jwt_secret

# Payment Processing
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
```

## Usage

### Development

Start the development server:

```bash
pnpm dev
```

This will start both the frontend and backend development servers.

### Building for Production

```bash
pnpm build
```

This will create optimized production builds for both frontend and backend.

### Running Tests

```bash
# Run all tests
pnpm test

# Run unit tests
pnpm test:unit

# Run integration tests
pnpm test:integration

# Run end-to-end tests
pnpm test:e2e
```

### Linting and Formatting

```bash
# Lint the code
pnpm lint

# Fix linting issues
pnpm lint:fix

# Format code
pnpm format
```

## Project Structure

```
intentional/
├── apps/
│   ├── web/              # Frontend application
│   └── api/              # Backend API
├── packages/
│   ├── ui/               # Shared UI components
│   ├── config/           # Shared configuration
│   └── types/            # Shared TypeScript types
├── docs/                 # Documentation
└── .windsurf/            # Project rules and guidelines
```

## Development Workflow

### Package Management

Always use `pnpm` instead of npm or yarn:

```bash
# Install dependencies
pnpm install

# Add new packages
pnpm add <package-name>

# Add dev dependencies
pnpm add -D <package-name>

# Run scripts
pnpm run <script>

# Use pnpx instead of npx
pnpx <command>
```

### Code Standards

- Use TypeScript for all code
- Use `type` instead of `interface` for type definitions
- Keep files under 250 lines of code
- Follow functional programming principles
- Use TailwindCSS for styling
- Implement proper error handling
- Write unit tests for critical business logic

### Git Workflow

- Use conventional commit messages
- Create feature branches from `main`
- Open pull requests for code review
- Ensure all tests pass before merging
- Keep commits focused and atomic

## Scripts

| Script | Description |
|--------|-------------|
| `pnpm dev` | Start development servers |
| `pnpm build` | Build for production |
| `pnpm test` | Run all tests |
| `pnpm lint` | Lint the codebase |
| `pnpm format` | Format code with Prettier |
| `pnpm type-check` | Run TypeScript type checking |

## Deployment

### Frontend Deployment

The frontend is deployed to AWS S3 with CloudFront CDN:

```bash
pnpm deploy:frontend
```

### Backend Deployment

The backend is deployed as AWS Lambda functions:

```bash
pnpm deploy:backend
```

### Full Deployment

Deploy both frontend and backend:

```bash
pnpm deploy
```

## Contributing

We welcome contributions to Intentional! Here's how you can help:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests if applicable
5. Commit your changes
6. Push to your fork
7. Create a pull request

Please ensure your code follows our coding standards and passes all tests.

### Development Guidelines

- Follow the project's coding standards
- Write clear, descriptive commit messages
- Include tests for new features
- Update documentation as needed
- Keep pull requests focused on a single feature or bug fix

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For questions, suggestions, or support, please open an issue on GitHub or contact the development team.

---

*Intentional - Transforming portfolio creation through AI-powered guidance and personalization.*
