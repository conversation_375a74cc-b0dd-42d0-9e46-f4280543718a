import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as path from 'path';

type IntentionalBeStackProps = cdk.StackProps;

export class IntentionalBeStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: IntentionalBeStackProps) {
    super(scope, id, props);

    // DynamoDB Table for storing user data and portfolios
    const portfolioTable = new dynamodb.Table(this, 'PortfolioTable', {
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'portfolioId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // Only for development
    });

    // Lambda function for handling portfolio operations
    const portfolioHandler = new lambda.Function(this, 'PortfolioHandler', {
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'portfolio.handler',
      code: lambda.Code.fromAsset(path.join(__dirname, '../src')),
      environment: {
        TABLE_NAME: portfolioTable.tableName,
      },
    });

    // Grant Lambda function permissions to access DynamoDB
    portfolioTable.grantReadWriteData(portfolioHandler);

    // API Gateway for RESTful API
    const api = new apigateway.RestApi(this, 'PortfolioApi', {
      restApiName: 'Intentional Portfolio API',
      description: 'API for Intentional portfolio builder platform',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
      },
    });

    // API Gateway resource and methods
    const portfolio = api.root.addResource('portfolio');
    const portfolioIntegration = new apigateway.LambdaIntegration(portfolioHandler);
    
    portfolio.addMethod('GET', portfolioIntegration);   // Get all portfolios
    portfolio.addMethod('POST', portfolioIntegration);  // Create new portfolio
    
    const singlePortfolio = portfolio.addResource('{id}');
    singlePortfolio.addMethod('GET', portfolioIntegration);    // Get specific portfolio
    singlePortfolio.addMethod('PUT', portfolioIntegration);    // Update portfolio
    singlePortfolio.addMethod('DELETE', portfolioIntegration); // Delete portfolio

    // Output the API endpoint URL
    new cdk.CfnOutput(this, 'ApiEndpoint', {
      value: api.url,
      description: 'API Gateway endpoint URL',
    });
  }
}
