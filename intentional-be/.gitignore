# Dependencies
node_modules/
*.log

# TypeScript
*.tsbuildinfo

# CDK
cdk.out/
*.autoscale

# Testing
coverage/
.nyc_output/

# Environment files
.env*
!.env.example

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build outputs
dist/
build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Diagnostic reports
*.debug.log*
