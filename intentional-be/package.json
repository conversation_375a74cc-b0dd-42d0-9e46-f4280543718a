{"name": "intentional-be", "version": "1.0.0", "description": "Backend for Intentional - AI-powered portfolio builder", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "lint": "echo \"Lin<PERSON> not configured\" && exit 0", "type-check": "tsc --noEmit", "format:check": "prettier --check .", "cdk": "cdk"}, "dependencies": {"aws-cdk-lib": "^2.142.1", "constructs": "^10.3.0", "openai": "^5.13.1", "source-map-support": "^0.5.21", "zod": "^4.0.17"}, "devDependencies": {"@types/aws-lambda": "^8.10.152", "@types/jest": "^29.5.12", "@types/node": "^20.14.11", "aws-cdk": "^2.142.1", "jest": "^29.7.0", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "typescript": "~5.5.3"}}