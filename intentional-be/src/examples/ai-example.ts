/**
 * Example usage of the AI integration
 * This file demonstrates how to call the AI endpoints from the frontend
 */

// Define types for better type safety
type Message = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

type AIResponse = {
  role: 'assistant';
  content: string;
};

type AISendMessageResponse = {
  response: AIResponse;
};

/**
 * Send a message to the AI
 * @param messages Array of messages in the conversation
 * @param model OpenAI model to use
 * @returns Response from the AI
 */
export const sendAIMessage = async (
  messages: Message[],
  model?: string
): Promise<AIResponse> => {
  try {
    const response = await fetch('/ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        model,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json() as AISendMessageResponse;
    return data.response;
  } catch (error) {
    console.error('Error sending AI message:', error);
    throw error;
  }
};

/**
 * Send a message to the AI with fallback
 * @param messages Array of messages in the conversation
 * @param model OpenAI model to use
 * @returns Response from the AI or fallback message
 */
export const sendAIMessageWithFallback = async (
  messages: Message[],
  model?: string
): Promise<AIResponse> => {
  try {
    const response = await fetch('/ai/fallback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        model,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json() as AISendMessageResponse;
    return data.response;
  } catch (error) {
    console.error('Error sending AI message with fallback:', error);
    throw error;
  }
};

// Example usage:
/*
const exampleMessages: Message[] = [
  {
    role: 'system',
    content: 'You are a helpful assistant for portfolio creation.',
  },
  {
    role: 'user',
    content: 'Help me create a portfolio for a software engineer.',
  },
];

sendAIMessage(exampleMessages)
  .then((response: AIResponse) => {
    console.log('AI response:', response);
  })
  .catch((error: Error) => {
    console.error('Error:', error);
  });
*/
