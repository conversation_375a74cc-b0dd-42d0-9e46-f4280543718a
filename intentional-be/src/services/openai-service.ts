import OpenAI from 'openai';
import { z } from 'zod';

// Load environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;

// Validate environment variables
const envSchema = z.object({
  OPENAI_API_KEY: z.string().min(1, 'OpenAI API key is required'),
});

let openai: OpenAI | null = null;

/**
 * Initialize OpenAI client
 * @returns OpenAI client instance
 */
export const initializeOpenAI = (): OpenAI => {
  if (openai) {
    return openai;
  }

  try {
    // Validate environment variables
    envSchema.parse({ OPENAI_API_KEY });
    
    // Create OpenAI client
    openai = new OpenAI({
      apiKey: OPENAI_API_KEY,
    });
    
    console.log('OpenAI client initialized successfully');
    return openai;
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Environment validation error:', error.issues);
      throw new Error(`Invalid environment configuration: ${error.issues[0].message}`);
    }
    console.error('Failed to initialize OpenAI client:', error);
    throw new Error('Failed to initialize OpenAI client');
  }
};

/**
 * Get OpenAI client instance
 * @returns OpenAI client instance
 */
export const getOpenAIClient = (): OpenAI => {
  if (!openai) {
    return initializeOpenAI();
  }
  return openai;
};

/**
 * Send a message to OpenAI chat completion API
 * @param messages Array of messages in the conversation
 * @param model OpenAI model to use (default: gpt-4-turbo)
 * @returns Response from OpenAI
 */
export const sendChatCompletion = async (
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  model: string = 'gpt-4-turbo'
): Promise<OpenAI.Chat.Completions.ChatCompletion> => {
  try {
    const client = getOpenAIClient();
    
    const response = await client.chat.completions.create({
      model,
      messages,
      temperature: 0.7,
      max_tokens: 1000,
    });
    
    return response;
  } catch (error) {
    console.error('Error sending chat completion request:', error);
    throw new Error(`Failed to send chat completion request: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
