import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { handleAIRequest, handleAIRequestWithFallback } from './middleware/ai-middleware';

type Portfolio = {
  userId: string;
  portfolioId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  sections: PortfolioSection[];
};

type PortfolioSection = {
  id: string;
  type: 'about' | 'experience' | 'projects' | 'skills' | 'education';
  title: string;
  content: any;
  order: number;
};

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('Event: ', JSON.stringify(event, null, 2));
  
  try {
    // Handle AI requests
    if (event.path === '/ai' && event.httpMethod === 'POST') {
      return await handleAIRequest(event);
    }
    
    // Handle AI requests with fallback
    if (event.path === '/ai/fallback' && event.httpMethod === 'POST') {
      return await handleAIRequestWithFallback(event);
    }
    
    switch (event.httpMethod) {
      case 'GET':
        if (event.pathParameters?.id) {
          // Get specific portfolio
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
              message: 'Get specific portfolio',
              portfolioId: event.pathParameters.id,
            }),
          };
        } else {
          // Get all portfolios
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
              message: 'Get all portfolios',
              portfolios: [],
            }),
          };
        }
      
      case 'POST':
        // Create new portfolio
        return {
          statusCode: 201,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          body: JSON.stringify({
            message: 'Portfolio created successfully',
            portfolioId: 'new-portfolio-id',
          }),
        };
      
      case 'PUT':
        // Update portfolio
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          body: JSON.stringify({
            message: 'Portfolio updated successfully',
            portfolioId: event.pathParameters?.id,
          }),
        };
      
      case 'DELETE':
        // Delete portfolio
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          body: JSON.stringify({
            message: 'Portfolio deleted successfully',
            portfolioId: event.pathParameters?.id,
          }),
        };
      
      default:
        return {
          statusCode: 405,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
          body: JSON.stringify({
            message: 'Method not allowed',
          }),
        };
    }
  } catch (error) {
    console.error('Error: ', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        message: 'Internal server error',
      }),
    };
  }
};
