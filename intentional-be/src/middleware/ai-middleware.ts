import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { sendChatCompletion } from '../services/openai-service';

/**
 * Middleware for handling AI requests
 * @param event API Gateway event
 * @returns Promise<APIGatewayProxyResult>
 */
export const handleAIRequest = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse the request body
    const requestBody = JSON.parse(event.body || '{}');
    
    // Validate required fields
    if (!requestBody.messages || !Array.isArray(requestBody.messages)) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          error: 'Messages array is required',
        }),
      };
    }
    
    // Send request to OpenAI
    const response = await sendChatCompletion(requestBody.messages, requestBody.model);
    
    // Return successful response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        response: response.choices[0].message,
      }),
    };
  } catch (error) {
    console.error('Error in AI middleware:', error);
    
    // Return error response
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal server error',
      }),
    };
  }
};

/**
 * Middleware for handling AI requests with fallback responses
 * @param event API Gateway event
 * @returns Promise<APIGatewayProxyResult>
 */
export const handleAIRequestWithFallback = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    // Parse the request body
    const requestBody = JSON.parse(event.body || '{}');
    
    // Validate required fields
    if (!requestBody.messages || !Array.isArray(requestBody.messages)) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          error: 'Messages array is required',
        }),
      };
    }
    
    // Send request to OpenAI
    const response = await sendChatCompletion(requestBody.messages, requestBody.model);
    
    // Return successful response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        response: response.choices[0].message,
      }),
    };
  } catch (error) {
    console.error('Error in AI middleware:', error);
    
    // Return fallback response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        response: {
          role: 'assistant',
          content: 'I\'m currently experiencing technical difficulties. Please try again later.',
        },
      }),
    };
  }
};
