# Intentional Backend

AWS Serverless backend for the Intentional portfolio builder platform.

## Architecture

- AWS Lambda functions for business logic
- API Gateway for RESTful API management
- DynamoDB for NoSQL database
- CloudFormation/CDK for Infrastructure as Code

## Prerequisites

- AWS Account
- AWS CLI configured
- Node.js 18+
- CDK CLI installed

## AWS Configuration

1. Install AWS CLI:
   ```bash
   # For macOS/Linux
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   
   # For Windows
   # Download and run the installer from https://aws.amazon.com/cli/
   ```

2. Configure AWS credentials:
   ```bash
   aws configure
   ```
   
   You'll need to provide:
   - AWS Access Key ID
   - AWS Secret Access Key
   - Default region (e.g., us-east-1)
   - Default output format (json)

3. Install CDK CLI:
   ```bash
   npm install -g aws-cdk
   ```

## Project Setup

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Bootstrap CDK (first time only):
   ```bash
   cdk bootstrap
   ```

## Deployment

1. Synthesize the CloudFormation template:
   ```bash
   cdk synth
   ```

2. Deploy the stack:
   ```bash
   cdk deploy
   ```

## Development

- Add new Lambda functions in the `src/` directory
- Update the stack definition in `lib/intentional-be-stack.ts`
- Add tests in the `test/` directory

## OpenAI Integration

The backend includes OpenAI integration for AI-powered features:

1. **Setup**:
   - Create an `.env` file in the project root with your OpenAI API key:
     ```
     OPENAI_API_KEY=your_openai_api_key_here
     ```
   - The `.env` file is gitignored for security

2. **API Endpoints**:
   - `POST /ai` - Standard AI requests
   - `POST /ai/fallback` - AI requests with fallback responses for error handling

3. **Usage**:
   - Send a POST request with a JSON body containing:
     ```json
     {
       "messages": [
         {"role": "system", "content": "You are a helpful assistant."},
         {"role": "user", "content": "Hello, AI!"}
       ],
       "model": "gpt-4-turbo" // Optional, defaults to gpt-4-turbo
     }
     ```

4. **Services**:
   - `src/services/openai-service.ts` - OpenAI client and utility functions
   - `src/middleware/ai-middleware.ts` - Request handling middleware

5. **Error Handling**:
   - Environment validation with Zod
   - Fallback responses for API failures
   - Detailed error logging

## Testing

Run unit tests:
```bash
pnpm test
```
