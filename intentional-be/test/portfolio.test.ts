import { handler } from '../src/portfolio';
import { APIGatewayProxyEvent } from 'aws-lambda';

describe('Portfolio Lambda Function', () => {
  const mockEvent: Partial<APIGatewayProxyEvent> = {
    body: null,
    headers: {},
    multiValueHeaders: {},
    httpMethod: 'GET',
    isBase64Encoded: false,
    path: '/portfolio',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    resource: '/portfolio',
    requestContext: {
      accountId: 'test-account',
      apiId: 'test-api',
      authorizer: {},
      httpMethod: 'GET',
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        clientCert: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: null,
        userArn: null,
      },
      path: '/portfolio',
      protocol: 'HTTP/1.1',
      requestId: 'test-request-id',
      requestTimeEpoch: **********,
      resourceId: 'test-resource-id',
      resourcePath: '/portfolio',
      stage: 'test',
    },
  };

  it('should return all portfolios for GET request without id', async () => {
    const event = {
      ...mockEvent,
      httpMethod: 'GET',
      pathParameters: null,
    } as APIGatewayProxyEvent;
    
    const result = await handler(event);
    
    expect(result.statusCode).toBe(200);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).message).toBe('Get all portfolios');
  });
  
  it('should return specific portfolio for GET request with id', async () => {
    const event = {
      ...mockEvent,
      httpMethod: 'GET',
      pathParameters: {
        id: 'test-id',
      },
    } as APIGatewayProxyEvent;
    
    const result = await handler(event);
    
    expect(result.statusCode).toBe(200);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).message).toBe('Get specific portfolio');
  });
  
  it('should create portfolio for POST request', async () => {
    const event = {
      ...mockEvent,
      httpMethod: 'POST',
      body: JSON.stringify({
        title: 'Test Portfolio',
        description: 'Test Description',
      }),
    } as APIGatewayProxyEvent;
    
    const result = await handler(event);
    
    expect(result.statusCode).toBe(201);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).message).toBe('Portfolio created successfully');
  });
});
