import { APIGatewayProxyEvent } from 'aws-lambda';
import { handler } from '../src/portfolio';

// Mock the OpenAI service
jest.mock('../src/services/openai-service', () => ({
  sendChatCompletion: jest.fn().mockResolvedValue({
    choices: [{
      message: {
        role: 'assistant',
        content: 'This is a test response from the AI',
      },
    }],
  }),
}));

describe('AI Lambda Function', () => {
  const mockEvent: Partial<APIGatewayProxyEvent> = {
    body: null,
    headers: {},
    multiValueHeaders: {},
    httpMethod: 'POST',
    isBase64Encoded: false,
    path: '/ai',
    pathParameters: null,
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    resource: '/ai',
    requestContext: {
      accountId: 'test-account',
      apiId: 'test-api',
      authorizer: {},
      httpMethod: 'POST',
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        clientCert: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: null,
        userArn: null,
      },
      path: '/ai',
      protocol: 'HTTP/1.1',
      requestId: 'test-request-id',
      requestTimeEpoch: **********,
      resourceId: 'test-resource-id',
      resourcePath: '/ai',
      stage: 'test',
    },
  };

  it('should handle AI requests', async () => {
    const event = {
      ...mockEvent,
      path: '/ai',
      httpMethod: 'POST',
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Hello, AI!',
          },
        ],
      }),
    } as APIGatewayProxyEvent;

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).response.content).toBe('This is a test response from the AI');
  });

  it('should handle AI requests with fallback', async () => {
    const event = {
      ...mockEvent,
      path: '/ai/fallback',
      httpMethod: 'POST',
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Hello, AI!',
          },
        ],
      }),
    } as APIGatewayProxyEvent;

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).response.content).toBe('This is a test response from the AI');
  });

  it('should return error for invalid AI request', async () => {
    const event = {
      ...mockEvent,
      path: '/ai',
      httpMethod: 'POST',
      body: JSON.stringify({
        invalid: 'data',
      }),
    } as APIGatewayProxyEvent;

    const result = await handler(event);

    expect(result.statusCode).toBe(400);
    expect(result.headers?.['Content-Type']).toBe('application/json');
    expect(JSON.parse(result.body).error).toBe('Messages array is required');
  });
});
