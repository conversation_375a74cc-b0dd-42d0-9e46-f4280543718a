---
trigger: model_decision
---

# Shadcn UI Component Guide

## Installing shadcn-ui CLI

To use shadcn UI commands, first install the shadcn-ui CLI globally:

```bash
pnpm add -g shadcn-ui
```

## Initializing shadcn-ui

Navigate to your frontend directory and initialize shadcn-ui:

```bash
cd intentional-fe
npx shadcn-ui init
```

This will create a `components.json` file that configures the component library.

## Adding New Components

To add new components from the shadcn UI library, use the following command:

```bash
pnpm dlx shadcn@latest add [component name]
```

For example, to add a card component:

```bash
pnpm dlx shadcn@latest add button
```

This will automatically:
1. Download the component files
2. Place them in the correct directory (`src/components/ui`)
3. Install any required dependencies
4. Update the component registry

## Available Components

You can browse available components at https://ui.shadcn.com/components

Commonly used components include:
- `button` - Customizable button component
- `card` - Card container with header, content, and footer
- `input` - Form input field
- `label` - Form label
- `select` - Dropdown selection
- `textarea` - Multi-line text input
- `checkbox` - Checkbox input
- `radio-group` - Radio button group
- `switch` - Toggle switch
- `dialog` - Modal dialog
- `popover` - Popover content
- `tooltip` - Tooltip text
- `accordion` - Collapsible content sections
- `tabs` - Tabbed interface
- `avatar` - User avatar
- `badge` - Status indicator badge

## Component Naming Convention

All component files should use kebab-case naming convention:
- ✅ `src/components/ui/button.tsx`
- ✅ `src/components/ui/card.tsx`
- ✅ `src/components/ui/input.tsx`
- ❌ `src/components/ui/Button.tsx`
- ❌ `src/components/ui/Card.tsx`

## Customizing Components

After adding a component, you can customize it to fit your design system:
1. Modify the component file in `src/components/ui/[component-name].tsx`
2. Update the styling in `src/styles/globals.css` if needed
3. Add custom variants in the component file

## Best Practices

1. Always use the shadcn-ui CLI to add new components rather than manually copying files
2. Keep component files small and focused on a single responsibility
3. Use the existing design tokens and styling conventions
4. Follow the kebab-case naming convention for all files
5. Update imports when renaming or moving component files