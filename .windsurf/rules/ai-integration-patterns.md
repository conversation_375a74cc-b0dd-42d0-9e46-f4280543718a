---
trigger: model_decision
description: "OpenAI integration patterns and AI agent development guidelines"
globs:
---

# AI Integration & OpenAI Patterns

## OpenAI GPT-4 Integration
- Use **OpenAI GPT-4** for natural language processing and content generation
- Implement proper API key management using AWS Secrets Manager
- Use structured prompts for consistent AI responses
- Implement proper rate limiting and quota management
- Handle OpenAI API errors gracefully with fallback responses

## AI Agent Architecture
- Design conversational AI agents with clear context management
- Implement proper prompt engineering for consistent results
- Use function calling capabilities for structured interactions
- Maintain conversation history and context appropriately
- Implement proper AI response validation and sanitization

## Content Strategy AI Patterns
- **Project selection guidance** based on user's target roles and industry
- **Content optimization** for both ATS systems and human reviewers
- **Skill highlighting** with automated evidence matching
- **Achievement quantification** assistance with impact measurement
- **Template suggestions** based on user profile and career goals

## Intelligent Onboarding Implementation
- Design **dynamic questionnaires** that adapt based on user responses
- Implement **career path detection** algorithms
- Create **skills assessment** workflows with gap identification
- Build **goal setting** systems with timeline establishment
- Provide **industry-specific guidance** through AI recommendations

## AI Response Processing
- Validate all AI-generated content before presenting to users
- Implement content filtering for inappropriate or biased responses
- Use structured response formats (JSON) for programmatic handling
- Implement proper retry logic for failed AI requests
- Cache frequently requested AI responses for performance

## Personalization Engine
- Build user profile analysis for personalized recommendations
- Implement content relevance scoring algorithms
- Create adaptive learning systems that improve over time
- Use A/B testing for AI recommendation effectiveness
- Implement feedback loops for continuous AI improvement

## AI Performance Optimization
- Implement response caching for similar queries
- Use batch processing for bulk AI operations
- Optimize prompt length and complexity for faster responses
- Implement streaming responses for real-time user experience
- Monitor AI response times and quality metrics

## Error Handling & Fallbacks
- Implement graceful degradation when AI services are unavailable
- Provide meaningful fallback content for failed AI requests
- Log AI failures for analysis and improvement
- Implement human review workflows for edge cases
- Use circuit breaker patterns for AI service reliability

## AI Content Quality Assurance
- Implement content validation pipelines
- Use automated quality scoring for AI-generated content
- Implement bias detection and mitigation strategies
- Provide human oversight for critical AI decisions
- Maintain audit trails for AI-generated content

## Privacy & Ethics
- Implement data privacy protection for user inputs to AI
- Use data minimization principles for AI processing
- Implement proper consent management for AI features
- Ensure AI responses comply with privacy regulations
- Implement transparent AI decision-making processes

## AI Testing Strategies
- Create comprehensive test suites for AI prompts and responses
- Implement regression testing for AI model changes
- Use synthetic data for AI testing when appropriate
- Monitor AI performance in production environments
- Implement A/B testing for AI feature improvements

## Integration Patterns
- **Guidance AI**: Step-by-step portfolio creation assistance
- **Review AI**: Content quality analysis and improvement suggestions
- **Personalization AI**: Template and design recommendations
- **Assessment AI**: Skills evaluation and career path suggestions
- **Optimization AI**: Content enhancement for target audiences