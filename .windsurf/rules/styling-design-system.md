---
trigger: model_decision
description: "TailwindCSS and design system guidelines for consistent UI"
globs: *.tsx,*.css,*.scss
---

# Styling & Design System Guidelines

## TailwindCSS Standards
- Use **utility-first approach** with TailwindCSS classes exclusively
- Avoid custom CSS unless absolutely necessary
- Implement consistent spacing using Tailwind's spacing scale
- Use Tailwind's color palette for brand consistency
- Leverage Tailwind's responsive design utilities

## Shadcn UI Component Library
- Use **Shadcn UI** components as the primary building blocks
- Extend Shadcn components rather than creating from scratch
- Maintain consistency with Shadcn's design tokens
- Follow Shadcn's composition patterns
- Customize Shadcn components through proper theming

## Design System Architecture
- Implement a centralized design token system
- Use CSS custom properties for dynamic theming
- Create reusable component variants using class-variance-authority
- Maintain consistent typography scale across the application
- Implement proper color contrast ratios for accessibility

## Responsive Design Implementation
- Follow mobile-first design approach
- Use Tailwind's breakpoint system (sm, md, lg, xl, 2xl)
- Implement proper touch targets for mobile devices
- Ensure consistent spacing across all device sizes
- Test layouts on multiple screen sizes

## Animation & Motion Standards
- Use **Framer Motion** for complex animations and transitions
- Implement consistent easing curves across animations
- Respect user's motion preferences (prefers-reduced-motion)
- Keep animations purposeful and enhance user experience
- Use appropriate animation durations (fast: 150ms, normal: 300ms, slow: 500ms)

## Iconography Guidelines
- Use **Lucide Icons** exclusively for consistent icon style
- Maintain consistent icon sizes (16px, 20px, 24px standards)
- Implement proper icon accessibility with aria-labels
- Use semantic icon choices that match user expectations
- Maintain consistent icon stroke width

## Color System
- Implement semantic color naming (primary, secondary, success, warning, error)
- Use Tailwind's color palette with proper shade variations
- Ensure WCAG AA compliance for color contrast
- Implement proper focus states with visible focus indicators
- Support both light and dark theme variants

## Typography System
- Use consistent font families across the application
- Implement proper heading hierarchy (h1-h6)
- Maintain consistent line heights and letter spacing
- Use appropriate font weights for emphasis
- Implement proper text color contrast for readability

## Layout & Spacing
- Use consistent spacing scale (4px, 8px, 16px, 24px, 32px, 48px, 64px)
- Implement proper container max-widths for content
- Use CSS Grid and Flexbox through Tailwind utilities
- Maintain consistent padding and margin patterns
- Implement proper visual hierarchy through spacing

## Component Design Patterns
- Create compound components for complex UI patterns
- Use composition over configuration for flexibility
- Implement proper loading and empty states
- Design consistent form field appearances
- Create reusable card and panel components

## Accessibility Standards
- Implement proper ARIA labels and descriptions
- Ensure keyboard navigation works throughout the application
- Maintain proper focus management in modal dialogs
- Use semantic HTML elements appropriately
- Implement screen reader friendly content structure

## Performance Considerations
- Purge unused Tailwind classes in production builds
- Optimize font loading with proper font-display strategies
- Use CSS containment for performance-critical components
- Implement proper image optimization and lazy loading
- Minimize layout shifts with proper sizing

## Brand Guidelines
- Implement consistent brand colors throughout the application
- Use professional color schemes aligned with portfolio industry standards
- Maintain consistent visual weight and balance
- Create engaging but professional user interfaces
- Implement subtle brand touches without overwhelming content

## Form Design Standards
- Create consistent form field appearances
- Implement proper validation states (error, success, warning)
- Use consistent button styles and hierarchy
- Implement proper form spacing and alignment
- Create accessible form labels and help text