---
trigger: model_decision
description: "Backend AWS serverless and Node.js development patterns"
globs: *.ts,*.js
---

# Backend AWS Serverless & Node.js Standards

## AWS Serverless Architecture
- Use **AWS Lambda** for business logic execution
- Implement **API Gateway** for RESTful API management
- Use **CloudFormation/CDK** for Infrastructure as Code
- Leverage **DynamoDB** for NoSQL data storage
- Implement **CloudFront** and **S3** for static content delivery

## Lambda Function Standards
- Keep Lambda functions focused and under 250 lines
- Use proper error handling and logging
- Implement cold start optimization strategies
- Use environment variables for configuration
- Follow the principle of least privilege for IAM roles

## Express.js API Development
- Use **Express.js** with TypeScript for web application framework
- Implement proper middleware for authentication, logging, and error handling
- Use async/await for asynchronous operations
- Implement proper request validation using Zod schemas
- Follow RESTful API design principles

## DynamoDB Best Practices
- Design efficient partition and sort key strategies
- Implement proper indexing for query patterns
- Use batch operations for bulk data operations
- Implement proper error handling for database operations
- Use DynamoDB transactions for multi-item operations

## Security & Authentication
- Implement JWT-based authentication
- Use AWS Cognito for user management when appropriate
- Validate all inputs using Zod schemas
- Implement proper CORS configuration
- Use HTTPS everywhere and proper encryption

## Error Handling & Logging
- Implement centralized error handling middleware
- Use structured logging with proper log levels
- Include correlation IDs for request tracing
- Implement proper error responses with consistent format
- Use AWS CloudWatch for monitoring and alerting

## API Design Patterns
- Follow RESTful conventions for endpoint naming
- Implement proper HTTP status codes
- Use consistent response formats
- Implement proper pagination for list endpoints
- Version APIs appropriately

## Performance Optimization
- Implement connection pooling where applicable
- Use proper caching strategies (Redis, DynamoDB DAX)
- Optimize database queries and access patterns
- Implement request/response compression
- Monitor and optimize Lambda cold start times

## Integration Patterns
- Use proper retry logic with exponential backoff
- Implement circuit breaker patterns for external services
- Use event-driven architecture with SQS/SNS when appropriate
- Implement proper health checks for services
- Use proper timeout configurations

## Testing Strategy
- Write unit tests for business logic
- Implement integration tests for API endpoints
- Mock external dependencies appropriately
- Use proper test data management strategies
- Implement contract testing for service boundaries

## Environment Management
- Use separate environments (dev, staging, production)
- Implement proper configuration management
- Use AWS Parameter Store or Secrets Manager for sensitive data
- Implement proper deployment pipelines
- Use blue-green or canary deployment strategies

## Monitoring & Observability
- Implement proper CloudWatch metrics and alarms
- Use AWS X-Ray for distributed tracing
- Implement custom business metrics
- Set up proper alerting for critical failures
- Use dashboard for system health visibility