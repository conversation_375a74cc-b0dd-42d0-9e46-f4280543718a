---
trigger: glob
description:
globs: *.ts,*.tsx,*.jsx
---

# Frontend React & TypeScript Standards

## TypeScript Guidelines
- Always use `type` instead of `interface` for type definitions
- Leverage <PERSON>od for runtime schema validation and type inference
- Use strict TypeScript configuration
- Prefer explicit return types for functions
- Use proper TypeScript generics where applicable

## React Component Standards
- Use functional components with hooks exclusively
- Implement proper component composition patterns
- Use React.memo() for performance optimization when needed
- Follow React Hook rules strictly
- Use custom hooks to encapsulate complex logic

## State Management
- Use **React Query** for server state management and caching
- Use **React Hook Form** for all form state and validation
- Leverage React's built-in state management (useState, useReducer) for local state
- Avoid prop drilling; use React Context for deeply nested state

## Component Architecture
- Keep components under 250 lines of code
- Break complex components into smaller, focused components
- Use composition over inheritance
- Implement proper error boundaries
- Follow single responsibility principle

## Styling with TailwindCSS
- Use utility-first approach with TailwindCSS classes
- Leverage **Shadcn UI** components as base building blocks
- Implement responsive design with Tailwind's responsive prefixes
- Use CSS custom properties for dynamic theming
- Avoid inline styles; prefer Tailwind utilities

## Animation & Interactions
- Use **Framer Motion** for complex animations and page transitions
- Implement accessible animations (respect prefers-reduced-motion)
- Keep animations performant and purposeful
- Use Framer Motion variants for coordinated animations

## Icons & Assets
- Use **Lucide Icons** for consistent iconography
- Optimize all images and assets for web performance
- Implement proper alt text for accessibility
- Use appropriate image formats (WebP, AVIF when supported)

## Performance Best Practices
- Implement code splitting with React.lazy() and Suspense
- Use React Query for efficient data fetching and caching
- Optimize bundle size with proper tree shaking
- Implement virtualization for long lists
- Use proper memoization strategies

## Form Handling
- Always use React Hook Form for form management
- Implement proper form validation with Zod schemas
- Provide clear error messages and field validation
- Implement proper accessibility for forms (labels, ARIA attributes)
- Handle form submission states appropriately

## Testing Approach
- Write unit tests for utility functions and hooks
- Implement integration tests for user workflows
- Use React Testing Library for component testing
- Mock external dependencies appropriately
- Maintain high test coverage for critical paths