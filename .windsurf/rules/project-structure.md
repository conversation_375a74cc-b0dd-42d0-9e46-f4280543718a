---
trigger: always_on
description:
globs:
---

# Intentional - AI-Powered Portfolio Builder Project Structure

## Project Overview
Intentional is an AI-powered portfolio builder platform that transforms complex portfolio creation into a guided, personalized experience using intelligent AI agents.

## Architecture & Tech Stack

### Frontend Stack
- **React.js** with TypeScript for component-based UI
- **Vite** as build tool for fast development
- **React Query** for server state management and caching
- **React Hook Form** for form state and validation
- **TailwindCSS** for utility-first styling
- **Shadcn UI** for consistent component library
- **Framer Motion** for animations and transitions
- **Lucide Icons** for consistent iconography
- **Zod** for TypeScript schema validation
- **Stripe/PayPal** for payment processing

### Backend Infrastructure
- **AWS Serverless Architecture** (Lambda, API Gateway, CloudFormation/CDK)
- **Node.js** with **Express.js** and **TypeScript**
- **DynamoDB** for NoSQL data storage
- **OpenAI GPT-4** integration for AI agent capabilities
- **AWS S3** for static assets, **<PERSON>Front** for CDN

## Project Structure Guidelines
- Use **pnpm** instead of npm/npx for package management
- Separate mock data from source code
- Keep code clean and eliminate redundant code
- Files should contain fewer than 250 lines of code
- Use `type` instead of `interface` in TypeScript
- Prefer TypeScript over JavaScript throughout the project
- No comments in source code by default unless absolutely necessary

## Core Features Focus
1. **AI Agent Core**: Intelligent onboarding, content strategy, personalized recommendations
2. **Portfolio Builder**: Guided workflow system, content management, design customization
3. **User Experience**: Step-by-step progression, smart validation, contextual help
4. **Monetization**: Freemium model with tiered pricing structure