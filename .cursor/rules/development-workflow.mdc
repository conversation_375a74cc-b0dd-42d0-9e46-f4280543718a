---
alwaysApply: true
---

# Development Workflow & Package Management

## Package Management Standards
- **Always use pnpm instead of npm or yarn** for package management
- Use `pnpx` instead of `npx` for running packages
- Maintain proper pnpm-lock.yaml file in version control
- Use pnpm workspaces for monorepo structure if applicable
- Keep package.json dependencies organized and up-to-date

## Code Quality Standards
- **Keep all files under 250 lines of code** - break down larger files
- **Eliminate all redundant code** - use DRY principles effectively
- **Separate mock data from source code** - maintain clean separation of concerns
- **Don't generate comments in source code by default** unless absolutely necessary
- **Output all code and documentation in English**

## File Organization
- Use clear, descriptive file and directory naming conventions
- Implement proper barrel exports (index.ts files) for clean imports
- Separate concerns: components, hooks, utils, types, constants
- Keep test files collocated with source files
- Organize assets and static files logically

## Development Best Practices
- Use **sequential thinking tool for complex tasks** when problem-solving
- **Use `type` instead of `interface`** for TypeScript type definitions
- Implement proper error boundaries and error handling
- Use consistent naming conventions (camelCase, PascalCase, kebab-case)
- Follow functional programming principles where appropriate

## Code Formatting & Linting
- Use Prettier for consistent code formatting
- Implement ESLint with TypeScript rules
- Use proper import ordering and organization
- Maintain consistent indentation and spacing
- Remove unused imports and variables

## Git Workflow
- Write clear, descriptive commit messages
- Use conventional commit format when applicable
- Create meaningful pull request descriptions
- Keep commits focused and atomic
- Use proper branch naming conventions

## Environment Management
- Use proper environment variable management
- Implement different configurations for development, staging, production
- Use proper secret management for sensitive data
- Maintain proper .gitignore for project files
- Use proper environment variable validation

## Performance Monitoring
- Implement proper logging for debugging and monitoring
- Use performance profiling tools during development
- Monitor bundle sizes and optimize accordingly
- Implement proper error tracking and reporting
- Use proper analytics for user behavior insights

## Security Practices
- Implement proper input validation and sanitization
- Use secure authentication and authorization patterns
- Keep dependencies updated and secure
- Implement proper CORS and security headers
- Use proper secret management practices

## Testing Strategy
- Write tests for critical business logic
- Use proper test data management
- Implement proper mocking strategies
- Maintain reasonable test coverage
- Use proper test naming and organization

## Documentation Standards
- Write clear README files for project setup
- Document complex algorithms and business logic
- Maintain API documentation for backend services
- Use proper TypeScript documentation comments when needed
- Keep documentation up-to-date with code changes