name: CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies (Frontend)
      run: cd intentional-fe && pnpm install
      
    - name: Install dependencies (Backend)
      run: cd intentional-be && pnpm install
      
    - name: Run linting (Frontend)
      run: cd intentional-fe && pnpm lint
      
    - name: Run type checking (Frontend)
      run: cd intentional-fe && pnpm type-check
      
    - name: Run type checking (Backend)
      run: cd intentional-be && pnpm type-check
      
    - name: Run tests (Frontend)
      run: cd intentional-fe && pnpm test
      
    - name: Run tests (Backend)
      run: cd intentional-be && pnpm test
      
    - name: Build frontend
      run: cd intentional-fe && pnpm build
      
    - name: Build backend
      run: cd intentional-be && pnpm build
