name: Code Quality

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies (Frontend)
      run: cd intentional-fe && pnpm install
      
    - name: Install dependencies (Backend)
      run: cd intentional-be && pnpm install
      
    - name: Run ESLint (Frontend)
      run: cd intentional-fe && pnpm lint
      
    - name: Run ESLint (Backend)
      run: cd intentional-be && pnpm lint
      
    - name: Check TypeScript types (Frontend)
      run: cd intentional-fe && pnpm type-check
      
    - name: Check TypeScript types (Backend)
      run: cd intentional-be && pnpm type-check
      
  format:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies (Frontend)
      run: cd intentional-fe && pnpm install
      
    - name: Install dependencies (Backend)
      run: cd intentional-be && pnpm install
      
    - name: Check code formatting (Frontend)
      run: cd intentional-fe && pnpm format:check
      
    - name: Check code formatting (Backend)
      run: cd intentional-be && pnpm format:check
      
  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies (Frontend)
      run: cd intentional-fe && pnpm install
      
    - name: Install dependencies (Backend)
      run: cd intentional-be && pnpm install
      
    - name: Run security audit (Frontend)
      run: cd intentional-fe && pnpm audit
      continue-on-error: true
      
    - name: Run security audit (Backend)
      run: cd intentional-be && pnpm audit
      continue-on-error: true
