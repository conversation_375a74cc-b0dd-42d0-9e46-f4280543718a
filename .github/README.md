# CI/CD Pipeline Documentation

This directory contains the GitHub Actions workflows that automate the testing, building, and deployment of the Intentional application.

## Workflows

### CI Pipeline (`ci.yml`)
- Runs on every push and pull request to the main branch
- Tests the application with multiple Node.js versions
- Runs linting and type checking
- Builds both frontend and backend

### Deploy Pipeline (`deploy.yml`)
- Deploys to development when pushing to the development branch
- Deploys to production when pushing to the main branch
- Handles both frontend and backend deployment

### Code Quality Pipeline (`code-quality.yml`)
- Runs ESLint and TypeScript type checking
- Checks code formatting
- Runs security audits

## Environments

The application is deployed to two environments:

1. **Development** - For testing new features
2. **Production** - For live user access

Environment-specific configurations are stored in `environments.json`.

## Secrets

The following secrets need to be configured in GitHub:

- `AWS_ACCESS_KEY_ID` - AWS access key for deployment
- `AWS_SECRET_ACCESS_KEY` - AWS secret key for deployment

## Deployment Process

1. Developers push code to feature branches
2. <PERSON>ull requests trigger the CI pipeline
3. After merging to main, the deploy pipeline runs automatically
4. For development deployments, push to the development branch
